import React, { useState, useEffect } from 'react';
import { getProductImageUrl, verifyImageAccess, STORAGE_CONFIG } from '../config/storage';
import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '../config/env';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

interface ProductImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  productId: string;
  imageUrl?: string;
}

export const ProductImage: React.FC<ProductImageProps> = ({
  productId,
  imageUrl,
  className = '',
  alt = '',
  ...props
}) => {
  const [src, setSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadImage = async () => {
      try {
        const url = await getProductImageUrl(productId, imageUrl);
        console.log(`Loading image for product ${productId}:`, url);
        
        // Verificar si la URL es accesible
        const isAccessible = await verifyImageAccess(url);
        if (!isAccessible) {
          console.warn(`Image not accessible: ${url}`);
        }
        
        setSrc(url);
      } catch (error) {
        console.error('Error loading product image:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();
  }, [productId, imageUrl]);

  if (isLoading) {
    return <div className={`animate-pulse bg-gray-200 ${className}`} />;
  }

  // Generar alt optimizado para SEO si no se proporciona
  const optimizedAlt = alt || `Producto EPP ${productId} - Equipo protección personal certificado`;

  return (
    <img
      src={src}
      alt={optimizedAlt}
      className={className}
      onError={(e) => {
        console.warn(`Image failed to load: ${src}`);
        // Intentar cargar la imagen por defecto
        const defaultImage = supabase.storage
          .from(STORAGE_CONFIG.STATIC_BUCKET)
          .getPublicUrl('placeholder-product.jpg')
          .data.publicUrl;
        e.currentTarget.src = defaultImage;
      }}
      {...props}
    />
  );
};
