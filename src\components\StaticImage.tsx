import React from 'react';
import { getPublicImageUrl } from '../config/storage';

interface StaticImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  name: string;
  fallback?: string;
}

export const StaticImage: React.FC<StaticImageProps> = ({
  name,
  fallback = 'placeholder-product.jpg',
  alt = '',
  ...props
}) => {
  const src = getPublicImageUrl(name, true);

  // Generar alt optimizado si no se proporciona
  const optimizedAlt = alt || generateOptimizedAlt(name);

  return (
    <img
      src={src}
      alt={optimizedAlt}
      onError={(e) => {
        e.currentTarget.src = getPublicImageUrl(fallback, true);
      }}
      {...props}
    />
  );
};

// Función helper para generar alt text optimizado para SEO
const generateOptimizedAlt = (imageName: string): string => {
  const name = imageName.toLowerCase().replace(/\.(jpg|jpeg|png|webp|avif)$/, '');

  // Mapeo de nombres de imagen a alt text optimizado
  const altMap: Record<string, string> = {
    'hero-bg': 'Trabajadores usando equipos de protección personal EPP en industria',
    'cr-work-logo': 'CR Work Seguridad Industrial - Proveedor EPP Argentina',
    'team1': 'Equipo profesional CR Work especialistas en seguridad industrial',
    'helmet-bg': 'Casco de seguridad industrial para protección craneana',
    'placeholder-product': 'Equipo de protección personal EPP certificado',
    '3m-promo': 'Productos 3M equipos protección personal EPP certificados',
    'ringo-promo': 'Productos Ringo equipos seguridad industrial Argentina',
    'epp-promo': 'Promoción equipos protección personal EPP ofertas especiales'
  };

  return altMap[name] || `${name.replace(/-/g, ' ')} - Equipos protección personal EPP`;
};