import React, { useEffect } from 'react';
import { insertSchema, generateProductSchema, generateCategorySchema, generateWebPageSchema } from '../lib/seo-schemas';
import type { Product } from '../types';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  type?: 'website' | 'product' | 'category' | 'article';
  product?: Product;
  products?: Product[];
  categoryName?: string;
  noindex?: boolean;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords = [],
  canonical,
  ogImage = 'https://cr-seg-ind.pages.dev/images/og-image-epp.jpg',
  type = 'website',
  product,
  products = [],
  categoryName,
  noindex = false
}) => {
  
  useEffect(() => {
    // Actualizar title
    if (title) {
      document.title = title;
    }

    // Actualizar meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription && description) {
      metaDescription.setAttribute('content', description);
    }

    // Actualizar meta keywords
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords && keywords.length > 0) {
      metaKeywords.setAttribute('content', keywords.join(', '));
    }

    // Actualizar canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (canonical) {
      if (!canonicalLink) {
        canonicalLink = document.createElement('link');
        canonicalLink.setAttribute('rel', 'canonical');
        document.head.appendChild(canonicalLink);
      }
      canonicalLink.setAttribute('href', canonical);
    }

    // Actualizar Open Graph
    const updateOGMeta = (property: string, content: string) => {
      let ogMeta = document.querySelector(`meta[property="${property}"]`);
      if (!ogMeta) {
        ogMeta = document.createElement('meta');
        ogMeta.setAttribute('property', property);
        document.head.appendChild(ogMeta);
      }
      ogMeta.setAttribute('content', content);
    };

    if (title) updateOGMeta('og:title', title);
    if (description) updateOGMeta('og:description', description);
    if (ogImage) updateOGMeta('og:image', ogImage);
    if (canonical) updateOGMeta('og:url', canonical);

    // Actualizar Twitter Cards
    const updateTwitterMeta = (name: string, content: string) => {
      let twitterMeta = document.querySelector(`meta[name="${name}"]`);
      if (!twitterMeta) {
        twitterMeta = document.createElement('meta');
        twitterMeta.setAttribute('name', name);
        document.head.appendChild(twitterMeta);
      }
      twitterMeta.setAttribute('content', content);
    };

    if (title) updateTwitterMeta('twitter:title', title);
    if (description) updateTwitterMeta('twitter:description', description);
    if (ogImage) updateTwitterMeta('twitter:image', ogImage);

    // Configurar robots meta
    let robotsMeta = document.querySelector('meta[name="robots"]');
    if (!robotsMeta) {
      robotsMeta = document.createElement('meta');
      robotsMeta.setAttribute('name', 'robots');
      document.head.appendChild(robotsMeta);
    }
    
    const robotsContent = noindex 
      ? 'noindex, nofollow' 
      : 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1';
    robotsMeta.setAttribute('content', robotsContent);

    // Insertar Schema.org apropiado
    switch (type) {
      case 'product':
        if (product) {
          insertSchema(generateProductSchema(product), 'product-schema');
        }
        break;
      
      case 'category':
        if (categoryName && description) {
          insertSchema(generateCategorySchema(categoryName, description, products), 'category-schema');
        }
        break;
      
      case 'website':
      default:
        insertSchema(generateWebPageSchema(), 'webpage-schema');
        break;
    }

  }, [title, description, keywords, canonical, ogImage, type, product, products, categoryName, noindex]);

  return null; // Este componente no renderiza nada visible
};

export default SEOHead;

// Hook personalizado para SEO
export const useSEO = (seoProps: SEOHeadProps) => {
  return <SEOHead {...seoProps} />;
};

// Configuraciones predefinidas para diferentes tipos de páginas
export const SEO_CONFIGS = {
  home: {
    title: 'EPP Seguridad Industrial | Protección Personal Certificada | CR Work',
    description: '🛡️ EPP certificados para seguridad industrial. Cascos, guantes, anteojos 3M, MSA. ✅ Envío gratis +$50k ⚡ Entrega 24-48hs 📞 Asesoramiento técnico',
    keywords: ['EPP', 'seguridad industrial', 'protección personal', 'cascos', 'guantes', 'anteojos', '3M', 'MSA', 'Argentina'],
    canonical: 'https://cr-seg-ind.pages.dev/',
    type: 'website' as const
  },
  
  catalog: {
    title: 'Catálogo EPP | Equipos de Protección Personal | CR Work',
    description: 'Catálogo completo de equipos de protección personal. Cascos, guantes, anteojos, calzado de seguridad y más. Productos certificados de las mejores marcas.',
    keywords: ['catálogo EPP', 'equipos protección personal', 'productos seguridad', 'cascos', 'guantes', 'calzado seguridad'],
    canonical: 'https://cr-seg-ind.pages.dev/catalogo',
    type: 'category' as const
  },

  about: {
    title: 'Nosotros | CR Work Seguridad Industrial | Empresa EPP Argentina',
    description: 'Conoce CR Work, empresa líder en equipos de protección personal y seguridad industrial en Argentina. Más de 4 años brindando soluciones de seguridad.',
    keywords: ['CR Work', 'empresa EPP', 'seguridad industrial Argentina', 'nosotros', 'historia empresa'],
    canonical: 'https://cr-seg-ind.pages.dev/nosotros',
    type: 'website' as const
  }
};

// Función helper para generar títulos SEO optimizados
export const generateSEOTitle = (productName: string, category?: string, brand?: string): string => {
  const parts = [productName];
  
  if (brand && !productName.includes(brand)) {
    parts.push(brand);
  }
  
  if (category) {
    parts.push(category);
  }
  
  parts.push('CR Work');
  
  const title = parts.join(' | ');
  
  // Truncar a 60 caracteres si es necesario
  return title.length > 60 ? title.substring(0, 57) + '...' : title;
};

// Función helper para generar descripciones SEO optimizadas
export const generateSEODescription = (productName: string, description?: string, price?: number): string => {
  let desc = `${productName} - `;
  
  if (description) {
    desc += description.substring(0, 100);
  } else {
    desc += 'Equipo de protección personal certificado para seguridad industrial';
  }
  
  if (price) {
    desc += ` | $${price}`;
  }
  
  desc += ' | Envío gratis +$50k | CR Work';
  
  // Truncar a 160 caracteres
  return desc.length > 160 ? desc.substring(0, 157) + '...' : desc;
};
