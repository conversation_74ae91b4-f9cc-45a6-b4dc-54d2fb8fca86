import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Verifica y corrige la estructura de la base de datos
 */
async function verifyDatabaseStructure() {
  console.log('🔍 Verificando estructura de la base de datos...');
  
  try {
    // 1. Verificar que la tabla products existe y tiene las columnas necesarias
    console.log('\n📋 Verificando tabla products...');
    
    const { data: tableInfo, error: tableError } = await supabase
      .from('products')
      .select('*')
      .limit(1);
    
    if (tableError) {
      console.error('❌ Error accediendo a la tabla products:', tableError.message);
      return false;
    }
    
    if (!tableInfo || tableInfo.length === 0) {
      console.log('⚠️  La tabla products está vacía');
    } else {
      console.log('✅ Tabla products accesible');
      
      // Verificar columnas necesarias
      const sampleProduct = tableInfo[0];
      const requiredColumns = ['categories', 'industries', 'tags', 'properties'];
      const missingColumns = [];
      
      requiredColumns.forEach(column => {
        if (!(column in sampleProduct)) {
          missingColumns.push(column);
        }
      });
      
      if (missingColumns.length > 0) {
        console.log(`⚠️  Columnas faltantes: ${missingColumns.join(', ')}`);
        console.log('💡 Ejecuta la migración add_product_tags_and_properties.sql');
      } else {
        console.log('✅ Todas las columnas necesarias están presentes');
      }
    }
    
    // 2. Verificar productos sin categorías asignadas
    console.log('\n🔍 Verificando productos sin categorías...');
    
    const { data: productsWithoutCategories, error: categoriesError } = await supabase
      .from('products')
      .select('id, name, category, categories')
      .or('categories.is.null,categories.eq.{}');
    
    if (categoriesError) {
      console.error('❌ Error verificando categorías:', categoriesError.message);
    } else {
      console.log(`📊 Productos sin categorías asignadas: ${productsWithoutCategories.length}`);
      
      if (productsWithoutCategories.length > 0) {
        console.log('📝 Productos que necesitan categorías:');
        productsWithoutCategories.slice(0, 5).forEach(product => {
          console.log(`   - ${product.name} (categoría actual: ${product.category || 'ninguna'})`);
        });
        if (productsWithoutCategories.length > 5) {
          console.log(`   ... y ${productsWithoutCategories.length - 5} más`);
        }
      }
    }
    
    // 3. Verificar productos sin tags
    console.log('\n🏷️  Verificando productos sin tags...');
    
    const { data: productsWithoutTags, error: tagsError } = await supabase
      .from('products')
      .select('id, name, tags')
      .or('tags.is.null,tags.eq.{}');
    
    if (tagsError) {
      console.error('❌ Error verificando tags:', tagsError.message);
    } else {
      console.log(`📊 Productos sin tags asignados: ${productsWithoutTags.length}`);
    }
    
    // 4. Verificar productos sin propiedades
    console.log('\n⚙️  Verificando productos sin propiedades...');
    
    const { data: productsWithoutProperties, error: propertiesError } = await supabase
      .from('products')
      .select('id, name, properties')
      .or('properties.is.null,properties.eq.{}');
    
    if (propertiesError) {
      console.error('❌ Error verificando propiedades:', propertiesError.message);
    } else {
      console.log(`📊 Productos sin propiedades asignadas: ${productsWithoutProperties.length}`);
    }
    
    // 5. Verificar función de búsqueda avanzada
    console.log('\n🔍 Verificando función de búsqueda avanzada...');
    
    try {
      const { data: searchResult, error: searchError } = await supabase
        .rpc('search_products_advanced', {
          search_term: '',
          category_filters: [],
          industry_filters: [],
          tag_filters: [],
          property_filters: {}
        });
      
      if (searchError) {
        console.error('❌ Función search_products_advanced no disponible:', searchError.message);
        console.log('💡 Ejecuta la migración add_product_tags_and_properties.sql');
      } else {
        console.log('✅ Función de búsqueda avanzada disponible');
      }
    } catch (error) {
      console.error('❌ Error probando función de búsqueda:', error.message);
    }
    
    // 6. Resumen y recomendaciones
    console.log('\n📋 RESUMEN Y RECOMENDACIONES:');
    
    const totalProducts = await getTotalProductsCount();
    const needsMigration = productsWithoutCategories.length > 0 || 
                          productsWithoutTags.length > 0 || 
                          productsWithoutProperties.length > 0;
    
    console.log(`📦 Total de productos: ${totalProducts}`);
    
    if (needsMigration) {
      console.log('\n⚠️  ACCIÓN REQUERIDA:');
      console.log('1. Ejecuta el script de migración: node scripts/migrateCategoriesAndTags.js');
      console.log('2. Verifica que los filtros funcionen correctamente');
      console.log('3. Considera poblar más productos de ejemplo si es necesario');
    } else {
      console.log('\n✅ La base de datos está correctamente configurada para los filtros');
    }
    
    return !needsMigration;
    
  } catch (error) {
    console.error('💥 Error general verificando la base de datos:', error);
    return false;
  }
}

/**
 * Obtiene el total de productos
 */
async function getTotalProductsCount() {
  try {
    const { count, error } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });
    
    if (error) throw error;
    return count || 0;
  } catch (error) {
    console.error('Error obteniendo total de productos:', error);
    return 0;
  }
}

/**
 * Función para mostrar estadísticas detalladas
 */
async function showDetailedStats() {
  console.log('\n📊 ESTADÍSTICAS DETALLADAS:');
  
  try {
    // Categorías más comunes
    const { data: categoryStats } = await supabase
      .from('products')
      .select('category');
    
    if (categoryStats) {
      const categoryCounts = {};
      categoryStats.forEach(product => {
        const category = product.category || 'Sin categoría';
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
      });
      
      console.log('\n📂 Categorías actuales:');
      Object.entries(categoryCounts)
        .sort(([,a], [,b]) => b - a)
        .forEach(([category, count]) => {
          console.log(`   ${category}: ${count} productos`);
        });
    }
    
    // Marcas más comunes
    const { data: brandStats } = await supabase
      .from('products')
      .select('brand');
    
    if (brandStats) {
      const brandCounts = {};
      brandStats.forEach(product => {
        const brand = product.brand || 'Sin marca';
        brandCounts[brand] = (brandCounts[brand] || 0) + 1;
      });
      
      console.log('\n🏷️  Marcas:');
      Object.entries(brandCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .forEach(([brand, count]) => {
          console.log(`   ${brand}: ${count} productos`);
        });
    }
    
  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
  }
}

// Ejecutar verificación
if (import.meta.url === `file://${process.argv[1]}`) {
  verifyDatabaseStructure()
    .then(async (isHealthy) => {
      await showDetailedStats();
      
      if (isHealthy) {
        console.log('\n🎉 ¡Base de datos verificada exitosamente!');
      } else {
        console.log('\n⚠️  Se encontraron problemas que requieren atención');
      }
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal en la verificación:', error);
      process.exit(1);
    });
}

export { verifyDatabaseStructure, showDetailedStats };
