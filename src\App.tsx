import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { HardHat, Shield, Factory, Settings, Users, Building2 } from 'lucide-react';
import Navbar from './components/Navbar';
import Hero from './components/Hero';
import ScrollToTopButton from './components/ScrollToTopButton';
import WhatsAppButton from './components/WhatsAppButton';
import Pictogram from './components/Pictogram';
import IndustriesSection from './components/IndustriesSection';
import Cart from './components/Cart';
import WelcomePopup from './components/WelcomePopup';
import Catalog from './components/Catalog';
import ProductDetail from './components/ProductDetail';
import UserRegistration from './components/UserRegistration';
import BrandCarousel from './components/BrandCarousel';
import './components/BrandCarousel.css';
import ClientCarousel from './components/ClientCarousel';
import PromoSlider from './components/PromoSlider';
import './styles/PromoSlider.css';
import './styles/responsive-images.css';
import { Product, CartItem } from './types';
import { Routes, Route } from 'react-router-dom';
import PersonalProtectionSection from './components/PersonalProtectionSection';
import Nosotros from './components/Nosotros';
// import Ubicanos from './components/Ubicanos';
import OrderDetail from './components/OrderDetail';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { setupSupabaseListener } from './lib/supabase';
import { initGA, setupScrollTracking, setupLinkTracking } from './lib/analytics';
import { supabase } from './lib/supabase';
import { API_URL, IMAGES } from './config/constants';
import FeatureCard from './components/FeatureCard';
import FeaturedProducts from './components/FeaturedProducts';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// import { CategoriesGrid } from './components/CategoriesGrid';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoutes from './routes/AdminRoutes';
import UserProfile from './components/UserProfile';
import Orders from './components/Orders';
import Footer from './components/Footer';
import { PRODUCT_CATEGORY_CARDS, CTA_CARDS } from './config/featureCards';
import LoadingScreen from './components/LoadingScreen';
import StatsSection from './components/StatsSection';
import PPECategoryVisualizer from './components/PPECategoryVisualizer';
import CtaCards from './components/CtaCards';
import { getAllPpeVisualizerImageUrls, preloadImages } from './utils/imageUtils';
import { useFirstScrollPreload } from './hooks/useFirstScrollPreload';

const backgroundImageUrl = "https://mla-s1-p.mlstatic.com/D_NQ_NP_773577-MLA41041719255_032020-OO.webp";

interface NavbarProps {
  onCartClick: () => void;
  cartItemsCount: number;
  onClientLoginClick: () => void;
}

// Crear una instancia de QueryClient
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 5 * 60 * 1000, // 5 minutos
      cacheTime: 10 * 60 * 1000, // 10 minutos
      retry: 1
    },
  },
});

// Configurar el listener de Supabase
setupSupabaseListener(queryClient);

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();
  const [isInitialSiteLoadComplete, setIsInitialSiteLoadComplete] = useState(false);
  const isHomePage = location.pathname === '/';

  // Hook para precarga de imágenes EPP en el primer scroll
  const { hasScrolled, isPreloading, preloadComplete, preloadError } = useFirstScrollPreload({
    threshold: 100, // Activar después de 100px de scroll
    enabled: isHomePage && isInitialSiteLoadComplete // Solo en home y después de la carga inicial
  });

  // Log opcional del estado de precarga EPP (solo en desarrollo)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      if (hasScrolled) {
        console.log('🎯 Primer scroll detectado - precarga EPP iniciada');
      }
      if (isPreloading) {
        console.log('⏳ Precargando imágenes EPP...');
      }
      if (preloadComplete) {
        console.log('✅ Precarga EPP completada');
      }
      if (preloadError) {
        console.log('❌ Error en precarga EPP');
      }
    }
  }, [hasScrolled, isPreloading, preloadComplete, preloadError]);

  useEffect(() => {
    // Solo actualizar favicon una vez
    const favicon = document.querySelector('link[rel="icon"]');
    if (favicon) {
      favicon.setAttribute('href', IMAGES.FAVICON);
    }
  }, []);

  useEffect(() => {
    // Este efecto se ejecuta después de que la pantalla de carga inicial desaparece
    // NOTA: La precarga de imágenes EPP ahora se maneja con el primer scroll
    // Solo precargamos las imágenes del PPE Visualizer (industrias) automáticamente
    if (isInitialSiteLoadComplete) {
      const ppeUrls = getAllPpeVisualizerImageUrls();
      if (ppeUrls.length > 0) {
        console.log(`Iniciando precarga en segundo plano de ${ppeUrls.length} imágenes de PPE Visualizer (industrias).`);
        preloadImages(ppeUrls);
      }
    }
  }, [isInitialSiteLoadComplete]);

  const navigate = useNavigate();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [showWelcome, setShowWelcome] = useState(false);
  const [products, setProducts] = useState<Product[]>([]); 
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Mover fetchProducts fuera del useEffect para poder reutilizarlo si es necesario
  const fetchProducts = async () => {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*');

      if (error) throw error;
      setProducts(data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Cargar productos solo una vez al inicio
  useEffect(() => {
    fetchProducts();

    // Inicializar Google Analytics y tracking
    initGA();
    setupScrollTracking();
    setupLinkTracking();
  }, []); // Sin dependencias para que solo se ejecute una vez

  const handleAddToCart = (product: Product, quantity: number) => {
    setCartItems((prev) => {
      const existing = prev.find((item) => item._id === product.id);
      if (existing) {
        return prev.map((item) =>
          item._id === product.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      }
      return [...prev, { 
        _id: product.id,
        name: product.name,
        price: product.price,
        quantity: quantity,
        brand: product.brand,
        category: product.category,
        image_url: product.image_url
      }];
    });
  };

  const handleUpdateQuantity = useCallback((id: string, quantity: number) => {
    setCartItems((prev) => {
      return quantity === 0
        ? prev.filter((item) => item._id !== id)
        : prev.map((item) => (item._id === id ? { ...item, quantity } : item));
    });
  }, []);

  const handleRemoveItem = useCallback((id: string) => {
    setCartItems((prev) => prev.filter((item) => item._id !== id));
  }, []);

  const handleWelcomeSubmit = (
    data: { companyType: string; industry: string }
  ) => {
    console.log('Welcome data:', data);
    // Here you could send this data to your analytics or CRM system
  };
  const handleLoginClick = () => {
    setIsAuthenticated(false); // Muestra el login
  };

  const ProductDetailWrapper = () => {
    const { id } = useParams<{ id: string }>();
    const [product, setProduct] = useState<Product | null>(null);

    useEffect(() => {
      const fetchProduct = async () => {
        try {
          const { data, error } = await supabase
            .from('products')
            .select('*')
            .eq('id', id)
            .single();

          if (error) throw error;

          const fetchedProduct = {
            ...data,
            imageUrl: data.imageUrl || '/images/placeholder-product.jpg',
            technicalSpecs: data.technicalSpecs || {
              'Specification 1': 'Value 1',
              'Specification 2': 'Value 2'
            }
          };
          setProduct(fetchedProduct);
        } catch (error: any) {
          setError(error.message);
        }
      };

      if (id) {
        fetchProduct();
      }
    }, [id]);

    if (loading) {
      return <div>Loading product...</div>;
    }

    if (error) {
      return <div>Error: {error}</div>;
    }

    if (!product) {
      return <div>Product not found.</div>;
    }

    return <ProductDetail product={product} onAddToCart={handleAddToCart} />;
  };

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {isHomePage && isLoading ? (
          <LoadingScreen onLoadingComplete={() => {
            setIsLoading(false); // Oculta la pantalla de carga
            setIsInitialSiteLoadComplete(true); // Señala que la carga primaria ha finalizado
          }} />
        ) : (
          <div className="app flex flex-col min-h-screen">
            <Navbar
              onCartClick={() => setIsCartOpen(true)}
              cartItemsCount={cartItems.length}
            />
            <main className="flex-grow">
              <Routes>
                <Route
                  path="/"
                  element={
                    <>
                      <Hero />
                      <PromoSlider />
                      <BrandCarousel />
                      
                      
                      {/* Featured Products */}
                      <FeaturedProducts 
                        onAddToCart={handleAddToCart} 
                        isAuthenticated={isAuthenticated} 
                      />
                      <StatsSection />
                      {/* Contenedor para EPP - Visualizador por categorías */}
                      <div className="w-full">
                        <PPECategoryVisualizer />
                      </div>

                      {/* Feature Cards como sección independiente a pantalla completa */}
                      <div className="min-h-[calc(100vh-60px)] bg-gray-50 py-8"> {/* Ajustado para considerar altura del header */}
                        <div className="container mx-auto px-4">
                          <h2 className="text-4xl font-bold text-gray-800 mb-8 text-center">
                            Soluciones para cada industria
                          </h2>
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                            {PRODUCT_CATEGORY_CARDS.map((card, index) => (
                              <FeatureCard
                                key={index}
                                icon={card.icon}
                                title={card.title}
                                description={card.description}
                                onClick={() => navigate(card.path)}
                                highlight={card.highlight}
                                imageUrl={card.imageUrl}
                              />
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Nueva sección para CTAs destacados */}
                      <CtaCards cards={CTA_CARDS} />

                      <ClientCarousel />
                    </>
                  }
                />
                <Route
                  path="/catalog"
                  element={<Catalog onAddToCart={handleAddToCart} />}
                />
                <Route 
                  path="/product/:id" 
                  element={<ProductDetailWrapper onAddToCart={handleAddToCart} />} 
                />
                <Route path="/register" element={<UserRegistration />} />
                <Route path="/nosotros" element={<Nosotros />} />
                {/* <Route path="/ubicanos" element={<Ubicanos />} /> */}
                <Route 
                  path="/order-detail" 
                  element={<OrderDetail items={cartItems} />} 
                />
                <Route element={<ProtectedRoute />}>
                  <Route path="/admin/*" element={<AdminRoutes />} />
                  <Route path="/perfil" element={<UserProfile />} />
                  <Route path="/pedidos" element={<Orders />} />
                </Route>
              </Routes>
            </main>
            <Footer />
            <Cart
              isOpen={isCartOpen}
              onClose={() => setIsCartOpen(false)}
              items={cartItems}
              onUpdateQuantity={(id: string, quantity: number) => handleUpdateQuantity(id, quantity)}
              onRemoveItem={(id: string) => handleRemoveItem(id)}
            />
            <WelcomePopup
              isOpen={showWelcome}
              onClose={() => setShowWelcome(false)}
              onSubmit={handleWelcomeSubmit}
            />
            <ScrollToTopButton isCartOpen={isCartOpen} />
            <WhatsAppButton isCartOpen={isCartOpen} />
            <ToastContainer
              position="bottom-center"
              autoClose={3000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="light"
              style={{ marginBottom: '4rem' }} // Añadir margen inferior
            />
          </div>
        )}
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
